import React from 'react';
import DownloadIcon from './icons/DownloadIcon';

const InfoIcon: React.FC<{ className?: string }> = ({ className = 'h-5 w-5' }) => (
  <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
    <path strokeLinecap="round" strokeLinejoin="round" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

interface ImageCardProps {
  src: string;
  prompt: string;
  originalPrompt: string;
}

const ImageCard: React.FC<ImageCardProps> = ({ src, prompt, originalPrompt }) => {
  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = src;
    const safePrompt = originalPrompt.replace(/[^a-z0-9\u0600-\u06FF]/gi, '_').toLowerCase().slice(0, 30);
    link.download = `ai-image-${safePrompt || 'untitled'}.jpeg`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const wasTranslated = prompt !== originalPrompt && originalPrompt.trim() !== '';

  return (
    <div className="group relative overflow-hidden rounded-xl bg-gray-800 shadow-lg border border-gray-700">
      <img
        src={src}
        alt={originalPrompt}
        className="w-full h-auto object-contain transition-transform duration-500 ease-in-out group-hover:scale-105"
      />
      <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center p-4">
        <button
          onClick={handleDownload}
          className="bg-white/20 backdrop-blur-md text-white font-bold py-3 px-6 rounded-full flex items-center gap-2 transform hover:scale-110 transition-transform duration-300"
        >
          <DownloadIcon />
          <span>تحميل</span>
        </button>
      </div>
      {wasTranslated && (
         <div className="absolute top-2.5 right-2.5" title={`الوصف المستخدم (بعد الترجمة):\n"${prompt}"`}>
           <div className="bg-gray-900/60 backdrop-blur-sm text-white p-1.5 rounded-full cursor-help">
             <InfoIcon />
           </div>
        </div>
      )}
    </div>
  );
};

export default ImageCard;