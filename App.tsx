import React, { useState, useCallback } from 'react';
import { generateImages } from './services/geminiService';
import { AspectRatio, GeneratedResult, HistoryItem } from './types';
import LoadingSpinner from './components/LoadingSpinner';
import ImageCard from './components/ImageCard';
import useLocalStorage from './hooks/useLocalStorage';
import History from './components/History';

const SparklesIcon: React.FC<{ className?: string }> = ({ className = 'h-5 w-5' }) => (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
    >
      <path d="M9.5 2.5l1.5 4 4 1.5-4 1.5-1.5 4-1.5-4-4-1.5 4-1.5Z" />
      <path d="M18 6l2 4 4 2-4 2-2 4-2-4-4-2 4-2Z" />
      <path d="M6 18l-2 4-4 2 4 2 2 4 2-4 4-2-4-2Z" />
    </svg>
);

const aspectRatios: { label: string, value: AspectRatio }[] = [
  { label: 'مربع', value: '1:1' },
  { label: 'عريض', value: '16:9' },
  { label: 'طويل', value: '9:16' },
  { label: 'أفقي', value: '4:3' },
  { label: 'عمودي', value: '3:4' },
];

const surpriseMePrompts: string[] = [
  "قطة ترتدي نظارات شمسية صغيرة وتسترخي على الشاطئ، بأسلوب فن البكسل.",
  "مدينة مستقبلية عائمة في السحب عند غروب الشمس، بأسلوب السايبربانك.",
  "غابة مسحورة ومضيئة ليلاً مع فطر متوهج ومخلوقات أسطورية، تصوير سينمائي.",
  "رائد فضاء يكتشف حديقة غريبة على كوكب بعيد، لوحة زيتية مفصلة.",
  "مكتبة قديمة لا نهاية لها مع سلالم حلزونية وكتب طائرة، بأسلوب فانتازي.",
  "بورتريه لكلب كورجي يرتدي زي ملكي، بأسلوب عصر النهضة.",
  "سيارة رياضية كلاسيكية تطير عبر نفق من أضواء النيون.",
  "جزيرة على شكل سلحفاة عملاقة تسبح في المحيط.",
];

function App() {
  if (!process.env.TOGETHER_API_KEY) {
    return (
      <div className="min-h-screen bg-gray-900 font-sans text-white flex items-center justify-center p-4">
        <div className="max-w-lg w-full bg-gray-800/50 backdrop-blur-sm p-8 rounded-2xl shadow-2xl border border-gray-700 text-center">
          <h1 className="text-3xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-teal-400 via-cyan-500 to-sky-600 mb-4">
            مفتاح API مطلوب
          </h1>
          <p className="text-gray-300 mb-6 text-lg">
            لاستخدام هذا التطبيق، يجب تعيين مفتاح واجهة برمجة التطبيقات (API Key) الخاص بـ Together AI.
          </p>
          <p className="text-gray-400 mb-2 text-base">
            الرجاء تعيين مفتاحك في متغيرات البيئة للمشروع تحت اسم:
          </p>
          <div className="bg-gray-900 p-4 rounded-lg text-left mb-6 font-mono text-center">
            <code className="text-gray-300">
              <span className="text-pink-400">TOGETHER_API_KEY</span>=<span className="text-green-400">"your-key-here"</span>
            </code>
          </div>
          <p className="text-gray-400 mb-6 text-base">
            يمكنك الحصول على مفتاحك من لوحة تحكم Together AI.
          </p>
          <a
            href="https://api.together.xyz/settings/api-keys"
            target="_blank"
            rel="noopener noreferrer"
            className="w-full inline-block py-3 text-lg font-bold text-white bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg hover:from-teal-600 hover:to-cyan-700 focus:outline-none focus:ring-4 focus:ring-cyan-300 transition-all duration-300 transform hover:scale-105"
          >
            الحصول على مفتاح Together AI
          </a>
        </div>
      </div>
    );
  }
  
  const [prompt, setPrompt] = useState<string>('');
  const [negativePrompt, setNegativePrompt] = useState<string>('');
  const [numberOfImages, setNumberOfImages] = useState<number>(1);
  const [generatedResult, setGeneratedResult] = useState<GeneratedResult | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedAspectRatio, setSelectedAspectRatio] = useState<AspectRatio>('1:1');
  const [history, setHistory] = useLocalStorage<HistoryItem[]>('image-generation-history', []);


  const handleSurpriseMe = useCallback(() => {
    const randomPrompt = surpriseMePrompts[Math.floor(Math.random() * surpriseMePrompts.length)];
    setPrompt(randomPrompt);
  }, []);

  const handleGenerate = useCallback(async () => {
    if (!prompt || isLoading) return;

    setIsLoading(true);
    setError(null);
    setGeneratedResult(null);

    try {
      const result = await generateImages(prompt, negativePrompt, selectedAspectRatio, numberOfImages);
      const historyItem: HistoryItem = {
          ...result,
          id: `gen-${Date.now()}`,
          timestamp: Date.now(),
      };
      setGeneratedResult(result);
      setHistory(prevHistory => [historyItem, ...prevHistory]);
    } catch (err) {
      console.error(err);
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع. الرجاء المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  }, [prompt, negativePrompt, selectedAspectRatio, numberOfImages, isLoading, setHistory]);
  
  const viewHistoryItem = (item: HistoryItem) => {
    setGeneratedResult(item);
    setPrompt(item.originalPrompt);
    const resultsElement = document.getElementById('results-section');
    resultsElement?.scrollIntoView({ behavior: 'smooth', block: 'start' });
  };

  const deleteHistoryItem = (id: string) => {
    setHistory(prev => prev.filter(item => item.id !== id));
  };
  
  const clearHistory = () => {
    setHistory([]);
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white p-4 sm:p-6 md:p-8">
      <div className="max-w-4xl mx-auto">
        <header className="text-center mb-10">
          <h1 className="text-4xl sm:text-5xl font-extrabold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-600">
            مولد الصور بالذكاء الاصطناعي
          </h1>
          <p className="text-gray-400 mt-2 text-lg">
            حوّل أفكارك إلى صور مذهلة. أدخل وصفاً نصياً ودع الذكاء الاصطناعي يبدع.
          </p>
        </header>

        <main>
          <div className="bg-gray-800/50 backdrop-blur-sm p-6 rounded-2xl shadow-2xl border border-gray-700">
            <div className="space-y-6">
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label htmlFor="prompt" className="block text-lg font-medium text-gray-300">
                    أدخل وصف الصورة
                  </label>
                  <button onClick={handleSurpriseMe} disabled={isLoading} className="flex items-center gap-2 text-sm text-purple-400 hover:text-purple-300 transition-colors disabled:opacity-50">
                      <SparklesIcon />
                      <span>فاجئني</span>
                  </button>
                </div>
                <textarea
                  id="prompt"
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="مثال: رائد فضاء يركب حصاناً على سطح المريخ، بأسلوب الرسم الزيتي"
                  className="w-full h-28 p-4 bg-gray-900 border-2 border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-300 text-white placeholder-gray-500 resize-none"
                  disabled={isLoading}
                />
              </div>

              <div>
                <label htmlFor="negative-prompt" className="block text-lg font-medium text-gray-300 mb-2">
                    وصف سلبي (اختياري)
                </label>
                <textarea
                  id="negative-prompt"
                  value={negativePrompt}
                  onChange={(e) => setNegativePrompt(e.target.value)}
                  placeholder="مثال: جودة سيئة، تشوه، نص، علامات مائية"
                  className="w-full h-20 p-4 bg-gray-900 border-2 border-gray-700 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-300 text-white placeholder-gray-500 resize-none"
                  disabled={isLoading}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6 pt-2">
                <div>
                    <label className="block text-lg font-medium text-gray-300 mb-3">
                      أبعاد الصورة
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {aspectRatios.map(({ label, value }) => (
                        <button
                          key={value}
                          onClick={() => setSelectedAspectRatio(value)}
                          className={`px-3 py-2 text-sm font-semibold rounded-full transition-all duration-300 ${
                            selectedAspectRatio === value
                              ? 'bg-purple-600 text-white shadow-lg'
                              : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                          }`}
                          disabled={isLoading}
                        >
                          {label}
                        </button>
                      ))}
                    </div>
                </div>

                <div>
                    <label htmlFor="image-count" className="block text-lg font-medium text-gray-300 mb-3">
                        عدد الصور: <span className="font-bold text-purple-400">{numberOfImages}</span>
                    </label>
                    <input
                        id="image-count"
                        type="range"
                        min="1"
                        max="4"
                        step="1"
                        value={numberOfImages}
                        onChange={(e) => setNumberOfImages(Number(e.target.value))}
                        disabled={isLoading}
                        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer accent-purple-500"
                    />
                </div>
              </div>

              <button
                onClick={handleGenerate}
                disabled={isLoading || !prompt}
                className="w-full py-4 text-lg font-bold text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-4 focus:ring-purple-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 flex items-center justify-center gap-3"
              >
                {isLoading ? (
                  <>
                    <LoadingSpinner />
                    <span>جاري الإنشاء...</span>
                  </>
                ) : (
                  <span>
                    إنشاء{' '}
                    {numberOfImages === 1
                      ? 'الصورة'
                      : numberOfImages === 2
                      ? 'صورتين'
                      : `${numberOfImages} صور`}
                  </span>
                )}
              </button>
            </div>
          </div>
          
          <History
            history={history}
            onView={viewHistoryItem}
            onDelete={deleteHistoryItem}
            onClear={clearHistory}
          />
          
          <div id="results-section" className="mt-10">
            {error && (
              <div className="bg-red-500/20 border border-red-500 text-red-300 p-4 rounded-lg text-center">
                <p className="font-bold">خطأ!</p>
                <p>{error}</p>
              </div>
            )}
            
            {!generatedResult && !isLoading && !error && (
               <div className="text-center text-gray-500 py-10">
                 <p className="text-xl">ستظهر صورك المُنشأة هنا.</p>
               </div>
            )}

            {generatedResult && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mt-6">
                {generatedResult.images.map((src, index) => (
                  <ImageCard 
                      key={`${generatedResult.usedPrompt}-${index}`}
                      src={src} 
                      prompt={generatedResult.usedPrompt}
                      originalPrompt={generatedResult.originalPrompt}
                  />
                ))}
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  );
}

export default App;