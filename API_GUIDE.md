# 🔑 دليل الحصول على مفاتيح API

## 🎯 نظرة عامة

يدعم التطبيق الآن عدة خدمات لتوليد الصور:

1. **محلي (مجاني)** - يعمل على جهازك
2. **OpenAI DALL-E** - جودة عالية جداً
3. **Stability AI** - متوازن وسريع
4. **Hugging Face** - مجاني مع حدود

---

## 🔐 كيفية الحصول على مفاتيح API

### 1. OpenAI DALL-E

**الخطوات:**
1. اذهب إلى: https://platform.openai.com/
2. أنشئ حساب أو سجل دخول
3. اذهب إلى: https://platform.openai.com/api-keys
4. اضغط "Create new secret key"
5. انسخ المفتاح واحفظه بأمان

**التكلفة:**
- DALL-E 3: $0.040 لكل صورة (1024×1024)
- DALL-E 2: $0.020 لكل صورة (1024×1024)

**المميزات:**
- ✅ جودة عالية جداً
- ✅ فهم ممتاز للنصوص
- ✅ سرعة عالية
- ❌ مكلف نسبياً

---

### 2. Stability AI

**الخطوات:**
1. اذهب إلى: https://platform.stability.ai/
2. أنشئ حساب جديد
3. اذهب إلى: https://platform.stability.ai/account/keys
4. اضغط "Create API Key"
5. انسخ المفتاح

**التكلفة:**
- حوالي $0.05 لكل صورة
- رصيد مجاني $25 للمبتدئين

**المميزات:**
- ✅ جودة عالية
- ✅ سرعة جيدة
- ✅ تحكم متقدم
- ✅ رصيد مجاني

---

### 3. Hugging Face

**الخطوات:**
1. اذهب إلى: https://huggingface.co/
2. أنشئ حساب مجاني
3. اذهب إلى: https://huggingface.co/settings/tokens
4. اضغط "New token"
5. اختر "Read" permissions
6. انسخ المفتاح

**التكلفة:**
- مجاني مع حدود استخدام
- قد يكون بطيء في أوقات الذروة

**المميزات:**
- ✅ مجاني تماماً
- ✅ نماذج متنوعة
- ❌ قد يكون بطيء
- ❌ حدود استخدام

---

## 🚀 كيفية الاستخدام

### 1. تشغيل التطبيق
```bash
python ai_image_generator.py
```

### 2. اختيار الخدمة
- اختر نوع الخدمة من القائمة المنسدلة
- أدخل مفتاح API (إذا لزم الأمر)

### 3. إعداد الصورة
- اكتب وصف الصورة بالإنجليزية
- اختر النموذج والإعدادات
- اضغط "توليد الصورة"

---

## 💡 نصائح للاستخدام

### للمبتدئين:
- ابدأ بـ **Hugging Face** (مجاني)
- جرب النماذج المختلفة
- تعلم كتابة الأوصاف الجيدة

### للاستخدام المتقدم:
- استخدم **OpenAI DALL-E 3** للجودة العالية
- استخدم **Stability AI** للتوازن
- استخدم **المحلي** للخصوصية

### لتوفير المال:
- ابدأ بالخدمة المحلية
- استخدم Hugging Face للتجارب
- احفظ مفاتيح API بأمان

---

## 🔒 أمان مفاتيح API

### ⚠️ تحذيرات مهمة:
- لا تشارك مفاتيح API مع أحد
- لا تنشرها على الإنترنت
- احفظها في مكان آمن
- راقب استخدامك والتكاليف

### 🛡️ نصائح الأمان:
- استخدم مفاتيح منفصلة لكل مشروع
- ضع حدود إنفاق في حسابك
- احذف المفاتيح غير المستخدمة
- راجع فواتيرك بانتظام

---

## 🆘 استكشاف الأخطاء

### خطأ في مفتاح API:
- تأكد من صحة المفتاح
- تحقق من انتهاء صلاحيته
- تأكد من وجود رصيد كافي

### خطأ في الشبكة:
- تحقق من الاتصال بالإنترنت
- جرب مرة أخرى بعد قليل
- تأكد من عدم حظر الخدمة

### جودة ضعيفة:
- حسن وصف الصورة
- جرب نماذج مختلفة
- اضبط الإعدادات

---

## 📊 مقارنة الخدمات

| الخدمة | التكلفة | الجودة | السرعة | سهولة الاستخدام |
|---------|---------|--------|--------|------------------|
| محلي | مجاني | جيدة | بطيء | متوسط |
| OpenAI | مرتفع | ممتاز | سريع | سهل |
| Stability | متوسط | عالي | سريع | سهل |
| Hugging Face | مجاني | متوسط | متغير | سهل |

---

## 🎯 التوصيات

**للاستخدام الشخصي:**
- ابدأ بـ Hugging Face
- انتقل لـ OpenAI للمشاريع المهمة

**للاستخدام التجاري:**
- استخدم OpenAI أو Stability AI
- ضع ميزانية واضحة

**للتعلم:**
- استخدم جميع الخدمات
- قارن النتائج
- تعلم من التجربة
