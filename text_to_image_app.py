import gradio as gr
import torch
from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
from PIL import Image
import os
import time
import random
from datetime import datetime
from config import Config

class TextToImageGenerator:
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.pipe = None
        self.model_loaded = False

        # إنشاء مجلد الحفظ
        if not os.path.exists(Config.OUTPUT_DIR):
            os.makedirs(Config.OUTPUT_DIR)

    def load_model(self, model_choice="stable-diffusion-1.5"):
        """تحميل نموذج Stable Diffusion المجاني"""
        try:
            print(Config.MESSAGES["model_loading"])
            # اختيار النموذج
            model_id = Config.ALTERNATIVE_MODELS.get(model_choice, Config.MODEL_ID)
            
            self.pipe = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float16 if (self.device == "cuda" and Config.USE_FLOAT16) else torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )

            # تحسين الأداء
            self.pipe.scheduler = DPMSolverMultistepScheduler.from_config(
                self.pipe.scheduler.config
            )

            self.pipe = self.pipe.to(self.device)

            # تحسين استخدام الذاكرة
            if self.device == "cuda":
                try:
                    self.pipe.enable_memory_efficient_attention()
                    self.pipe.enable_xformers_memory_efficient_attention()
                except:
                    print("تحذير: لم يتم تفعيل تحسينات الذاكرة")

            self.model_loaded = True
            print(Config.MESSAGES["model_loaded"])
            return Config.MESSAGES["model_loaded"]
            
        except Exception as e:
            error_msg = f"{Config.MESSAGES['model_error']}: {str(e)}"
            print(error_msg)
            return error_msg

    def generate_image(self, prompt, negative_prompt="", num_inference_steps=None,
                      guidance_scale=None, width=None, height=None, seed=None, save_image=True):
        """توليد صورة من النص"""
        if not self.model_loaded:
            return None, Config.MESSAGES["model_not_loaded"]

        # استخدام القيم الافتراضية من الإعدادات
        num_inference_steps = num_inference_steps or Config.DEFAULT_STEPS
        guidance_scale = guidance_scale or Config.DEFAULT_GUIDANCE
        width = width or Config.DEFAULT_WIDTH
        height = height or Config.DEFAULT_HEIGHT
        
        try:
            # تعيين البذرة للحصول على نتائج قابلة للتكرار
            if seed is not None:
                generator = torch.Generator(device=self.device).manual_seed(seed)
            else:
                generator = None

            # توليد الصورة
            with torch.autocast(self.device):
                image = self.pipe(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    width=width,
                    height=height,
                    generator=generator
                ).images[0]

            # حفظ الصورة إذا كان مطلوباً
            if save_image:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"generated_{timestamp}.png"
                filepath = os.path.join(Config.OUTPUT_DIR, filename)
                image.save(filepath)
                print(f"تم حفظ الصورة في: {filepath}")

            return image, Config.MESSAGES["generation_success"]
            
        except Exception as e:
            error_msg = f"{Config.MESSAGES['generation_error']}: {str(e)}"
            print(error_msg)
            return None, error_msg

# إنشاء مثيل من المولد
generator = TextToImageGenerator()

def load_model_interface(model_choice):
    """واجهة تحميل النموذج"""
    return generator.load_model(model_choice)

def generate_image_interface(prompt, negative_prompt, steps, guidance, width, height, seed):
    """واجهة توليد الصورة"""
    if not prompt.strip():
        return None, Config.MESSAGES["empty_prompt"]

    # تحويل البذرة إلى رقم صحيح أو None
    try:
        seed = int(seed) if seed else None
    except:
        seed = None

    image, message = generator.generate_image(
        prompt=prompt,
        negative_prompt=negative_prompt,
        num_inference_steps=int(steps),
        guidance_scale=float(guidance),
        width=int(width),
        height=int(height),
        seed=seed
    )

    return image, message

def get_random_prompt():
    """الحصول على وصف عشوائي"""
    return random.choice(Config.EXAMPLE_PROMPTS)

def get_random_negative():
    """الحصول على نص سلبي عشوائي"""
    return random.choice(Config.EXAMPLE_NEGATIVE_PROMPTS)

# إنشاء واجهة Gradio
with gr.Blocks(title=Config.INTERFACE_TITLE, theme=gr.themes.Soft()) as app:
    gr.Markdown(f"""
    # 🎨 {Config.INTERFACE_TITLE}
    {Config.INTERFACE_DESCRIPTION}
    """)

    with gr.Row():
        with gr.Column():
            # اختيار النموذج
            model_choice = gr.Dropdown(
                choices=list(Config.ALTERNATIVE_MODELS.keys()),
                value="stable-diffusion-1.5",
                label="اختيار النموذج"
            )

            # تحميل النموذج
            load_btn = gr.Button("تحميل النموذج", variant="primary")
            load_status = gr.Textbox(label="حالة التحميل", interactive=False)

            gr.Markdown("### إعدادات الصورة")

            # أزرار الأمثلة
            with gr.Row():
                random_prompt_btn = gr.Button("وصف عشوائي", size="sm")
                random_negative_btn = gr.Button("نص سلبي عشوائي", size="sm")

            # إدخال النص
            prompt_input = gr.Textbox(
                label="وصف الصورة (بالإنجليزية)",
                placeholder="مثال: A beautiful sunset over mountains",
                lines=3
            )

            negative_prompt_input = gr.Textbox(
                label="ما لا تريده في الصورة (اختياري)",
                placeholder="مثال: blurry, low quality, distorted",
                lines=2
            )

            with gr.Row():
                steps_slider = gr.Slider(
                    minimum=Config.MIN_STEPS, maximum=Config.MAX_STEPS,
                    value=Config.DEFAULT_STEPS, step=1,
                    label="عدد خطوات التوليد"
                )
                guidance_slider = gr.Slider(
                    minimum=Config.MIN_GUIDANCE, maximum=Config.MAX_GUIDANCE,
                    value=Config.DEFAULT_GUIDANCE, step=0.5,
                    label="قوة التوجيه"
                )

            with gr.Row():
                width_slider = gr.Slider(
                    minimum=Config.MIN_SIZE, maximum=Config.MAX_SIZE,
                    value=Config.DEFAULT_WIDTH, step=64,
                    label="العرض"
                )
                height_slider = gr.Slider(
                    minimum=Config.MIN_SIZE, maximum=Config.MAX_SIZE,
                    value=Config.DEFAULT_HEIGHT, step=64,
                    label="الارتفاع"
                )

            with gr.Row():
                seed_input = gr.Number(
                    label="البذرة (اختياري - للحصول على نفس النتيجة)",
                    value=None
                )
                random_seed_btn = gr.Button("بذرة عشوائية", size="sm")
            
            generate_btn = gr.Button("توليد الصورة", variant="secondary")
        
        with gr.Column():
            # عرض النتائج
            output_image = gr.Image(label="الصورة المولدة")
            output_message = gr.Textbox(label="الرسائل", interactive=False)
    
    # ربط الأحداث
    load_btn.click(
        fn=load_model_interface,
        inputs=model_choice,
        outputs=load_status
    )

    generate_btn.click(
        fn=generate_image_interface,
        inputs=[
            prompt_input, negative_prompt_input, steps_slider,
            guidance_slider, width_slider, height_slider, seed_input
        ],
        outputs=[output_image, output_message]
    )

    # أحداث الأزرار الجديدة
    random_prompt_btn.click(
        fn=get_random_prompt,
        outputs=prompt_input
    )

    random_negative_btn.click(
        fn=get_random_negative,
        outputs=negative_prompt_input
    )

    random_seed_btn.click(
        fn=lambda: random.randint(1, 1000000),
        outputs=seed_input
    )
    
    gr.Markdown("""
    ### تعليمات الاستخدام:
    1. اضغط على "تحميل النموذج" أولاً (قد يستغرق بضع دقائق في المرة الأولى)
    2. اكتب وصفاً للصورة التي تريدها باللغة الإنجليزية
    3. اضبط الإعدادات حسب رغبتك
    4. اضغط على "توليد الصورة"
    
    ### نصائح:
    - استخدم وصفاً مفصلاً للحصول على نتائج أفضل
    - جرب قيم مختلفة للبذرة للحصول على تنويعات
    - قلل عدد الخطوات لتوليد أسرع (جودة أقل)
    """)

if __name__ == "__main__":
    print("🎨 مرحباً بك في مولد الصور بالذكاء الاصطناعي!")
    print("📋 تأكد من تثبيت جميع المتطلبات قبل البدء")
    print("🚀 جاري تشغيل التطبيق...")

    app.launch(
        server_name=Config.SERVER_HOST,
        server_port=Config.SERVER_PORT,
        share=Config.SHARE_GRADIO,
        inbrowser=True
    )
