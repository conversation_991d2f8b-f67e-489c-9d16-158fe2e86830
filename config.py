# إعدادات التطبيق
import os

class Config:
    # إعدادات النموذج
    MODEL_ID = "runwayml/stable-diffusion-v1-5"
    
    # نماذج بديلة يمكن استخدامها
    ALTERNATIVE_MODELS = {
        "stable-diffusion-1.5": "runwayml/stable-diffusion-v1-5",
        "stable-diffusion-2.1": "stabilityai/stable-diffusion-2-1",
        "openjourney": "prompthero/openjourney",
        "dreamlike-art": "dreamlike-art/dreamlike-diffusion-1.0"
    }
    
    # إعدادات الجهاز
    DEVICE = "cuda" if os.environ.get("CUDA_AVAILABLE", "false").lower() == "true" else "cpu"
    USE_FLOAT16 = True  # لتوفير الذاكرة
    
    # إعدادات التوليد الافتراضية
    DEFAULT_STEPS = 20
    DEFAULT_GUIDANCE = 7.5
    DEFAULT_WIDTH = 512
    DEFAULT_HEIGHT = 512
    
    # حدود الإعدادات
    MIN_STEPS = 5
    MAX_STEPS = 100
    MIN_GUIDANCE = 1.0
    MAX_GUIDANCE = 30.0
    MIN_SIZE = 128
    MAX_SIZE = 1024
    
    # إعدادات الواجهة
    INTERFACE_TITLE = "مولد الصور بالذكاء الاصطناعي"
    INTERFACE_DESCRIPTION = "تطبيق مجاني لتحويل النص إلى صورة باستخدام Stable Diffusion"
    
    # إعدادات الخادم
    SERVER_HOST = "0.0.0.0"
    SERVER_PORT = 7860
    SHARE_GRADIO = False  # تغيير إلى True لمشاركة الرابط عبر الإنترنت
    
    # مجلد حفظ الصور
    OUTPUT_DIR = "generated_images"
    
    # رسائل النظام
    MESSAGES = {
        "model_loading": "جاري تحميل النموذج...",
        "model_loaded": "تم تحميل النموذج بنجاح!",
        "model_error": "خطأ في تحميل النموذج",
        "generation_success": "تم توليد الصورة بنجاح!",
        "generation_error": "خطأ في توليد الصورة",
        "empty_prompt": "يرجى إدخال وصف للصورة",
        "model_not_loaded": "يجب تحميل النموذج أولاً"
    }
    
    # أمثلة على الأوصاف
    EXAMPLE_PROMPTS = [
        "A beautiful sunset over mountains with orange and pink clouds",
        "A cute cat sitting in a garden with colorful flowers",
        "A futuristic city with flying cars and neon lights",
        "A peaceful lake surrounded by autumn trees",
        "A magical forest with glowing mushrooms and fairy lights",
        "A vintage car driving through a desert landscape",
        "A cozy cabin in the snow with warm lights in the windows",
        "A dragon flying over a medieval castle"
    ]
    
    # أمثلة على النصوص السلبية
    EXAMPLE_NEGATIVE_PROMPTS = [
        "blurry, low quality, distorted, ugly, bad anatomy",
        "text, watermark, signature, logo",
        "dark, gloomy, scary, horror",
        "cartoon, anime, drawing, painting"
    ]
