# 📚 دليل استخدام مولد الصور بالذكاء الاصطناعي

## 🚀 البدء السريع

### 1. التثبيت
```bash
# تثبيت المتطلبات
pip install -r requirements.txt

# أو استخدم ملف التثبيت التلقائي
install.bat
```

### 2. فحص النظام
```bash
# فحص النظام والمتطلبات
python check_system.py
```

### 3. تشغيل التطبيق
```bash
# تشغيل التطبيق
python text_to_image_app.py

# أو استخدم ملف التشغيل
run.bat
```

## 🎯 كيفية الاستخدام

### الخطوة 1: تحميل النموذج
1. اختر النموذج المناسب من القائمة المنسدلة
2. اضغط على "تحميل النموذج"
3. انتظر حتى يكتمل التحميل (5-10 دقائق في المرة الأولى)

### الخطوة 2: كتابة الوصف
- اكتب وصفاً مفصلاً للصورة **بالإنجليزية**
- استخدم الصفات الوصفية والتفاصيل
- يمكنك استخدام زر "وصف عشوائي" للحصول على أمثلة

### الخطوة 3: ضبط الإعدادات
- **عدد الخطوات**: أكثر = جودة أفضل (لكن أبطأ)
- **قوة التوجيه**: أعلى = التزام أكبر بالوصف
- **الحجم**: أكبر = تفاصيل أكثر (يحتاج ذاكرة أكثر)

### الخطوة 4: توليد الصورة
- اضغط على "توليد الصورة"
- انتظر حتى تكتمل العملية
- ستظهر الصورة في الجانب الأيمن

## 💡 نصائح لكتابة أوصاف أفضل

### ✅ أوصاف جيدة:
```
A majestic lion with golden mane standing on African savanna at sunset, 
warm lighting, photorealistic, high detail, professional photography
```

```
Cozy cabin in snowy forest, warm lights glowing from windows, 
winter evening, peaceful atmosphere, detailed, cinematic lighting
```

### ❌ أوصاف ضعيفة:
```
cat
```

```
nice picture
```

### 🔑 عناصر الوصف الجيد:
1. **الموضوع الرئيسي**: ما تريد رؤيته
2. **الصفات**: ألوان، أحجام، مشاعر
3. **البيئة**: المكان والخلفية
4. **الإضاءة**: نوع الإضاءة والوقت
5. **الأسلوب**: نوع الفن أو التصوير

## ⚙️ شرح الإعدادات

### عدد خطوات التوليد (5-100)
- **5-15**: سريع جداً، جودة منخفضة
- **15-25**: متوازن، جودة جيدة
- **25-50**: جودة عالية، بطيء
- **50+**: جودة ممتازة، بطيء جداً

### قوة التوجيه (1-30)
- **1-5**: حرية إبداعية أكثر
- **7-10**: متوازن (مُوصى به)
- **10-15**: التزام قوي بالوصف
- **15+**: قد يؤدي لتشويه

### حجم الصورة
- **256x256**: سريع، ذاكرة قليلة
- **512x512**: متوازن (افتراضي)
- **768x768**: جودة عالية، ذاكرة أكثر
- **1024x1024**: أعلى جودة، يحتاج GPU قوي

### البذرة (Seed)
- **فارغ**: نتائج عشوائية في كل مرة
- **رقم محدد**: نفس النتيجة في كل مرة
- مفيد لتجربة إعدادات مختلفة على نفس الصورة

## 🎨 أنواع النماذج المتاحة

### Stable Diffusion 1.5
- **الأفضل للمبتدئين**
- سريع ومستقر
- جودة جيدة للاستخدام العام

### Stable Diffusion 2.1
- جودة أعلى
- أبطأ قليلاً
- أفضل للتفاصيل الدقيقة

### OpenJourney
- أسلوب فني مميز
- مُحسن للمناظر الطبيعية
- ألوان زاهية

### Dreamlike Art
- أسلوب حالم وفني
- مُحسن للصور الخيالية
- تأثيرات بصرية مميزة

## 🔧 استكشاف الأخطاء

### مشكلة: خطأ في الذاكرة
**الحلول:**
- قلل حجم الصورة إلى 256x256
- قلل عدد الخطوات إلى 10-15
- أغلق البرامج الأخرى
- أعد تشغيل التطبيق

### مشكلة: بطء شديد
**الحلول:**
- تأكد من وجود كرت رسوميات NVIDIA
- قلل عدد الخطوات
- استخدم حجم أصغر
- تحقق من درجة حرارة الجهاز

### مشكلة: جودة ضعيفة
**الحلول:**
- زد عدد الخطوات إلى 30-50
- استخدم وصفاً أكثر تفصيلاً
- جرب قيم مختلفة لقوة التوجيه
- أضف كلمات مثل "high quality, detailed"

### مشكلة: النتيجة لا تطابق الوصف
**الحلول:**
- زد قوة التوجيه
- اجعل الوصف أكثر تحديداً
- استخدم النص السلبي لاستبعاد ما لا تريده
- جرب نماذج مختلفة

## 📁 تنظيم الملفات

```
image/
├── text_to_image_app.py    # التطبيق الرئيسي
├── config.py               # ملف الإعدادات
├── check_system.py         # فحص النظام
├── examples.py             # أمثلة ونصائح
├── requirements.txt        # المتطلبات
├── install.bat            # ملف التثبيت
├── run.bat                # ملف التشغيل
├── generated_images/      # مجلد الصور المولدة
├── README.md              # دليل المشروع
└── TUTORIAL.md            # هذا الملف
```

## 🎯 أمثلة عملية

### مثال 1: منظر طبيعي
```
الوصف: Mountain landscape at golden hour with lake reflection, 
misty atmosphere, pine trees, warm colors, photorealistic

الإعدادات:
- خطوات: 25
- توجيه: 7.5
- حجم: 768x512
```

### مثال 2: بورتريه
```
الوصف: Portrait of elderly wise man with kind eyes, 
soft lighting, detailed wrinkles, warm smile, professional photography

الإعدادات:
- خطوات: 30
- توجيه: 8.5
- حجم: 512x768
```

### مثال 3: فن خيالي
```
الوصف: Magical forest with glowing mushrooms, fairy lights, 
mystical atmosphere, enchanted, fantasy art, vibrant colors

الإعدادات:
- خطوات: 35
- توجيه: 8.0
- حجم: 512x512
```

## 🆘 الحصول على المساعدة

إذا واجهت مشاكل:
1. شغل `check_system.py` للتأكد من النظام
2. تحقق من ملف `README.md` للمعلومات الأساسية
3. جرب الأمثلة في `examples.py`
4. تأكد من تحديث جميع المكتبات

---

**نصيحة أخيرة**: التجريب هو أفضل طريقة للتعلم! جرب أوصاف وإعدادات مختلفة لاكتشاف ما يعمل بشكل أفضل لاحتياجاتك.
