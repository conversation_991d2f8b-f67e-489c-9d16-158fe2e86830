import React, { useState } from 'react';
import { HistoryItem } from '../types';
import HistoryItemCard from './HistoryItem';
import HistoryIcon from './icons/HistoryIcon';
import TrashIcon from './icons/TrashIcon';

interface HistoryProps {
  history: HistoryItem[];
  onView: (item: HistoryItem) => void;
  onDelete: (id: string) => void;
  onClear: () => void;
}

const ChevronDownIcon: React.FC<{ className?: string }> = ({ className }) => (
    <svg xmlns="http://www.w3.org/2000/svg" className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
    </svg>
);


const History: React.FC<HistoryProps> = ({ history, onView, onDelete, onClear }) => {
  const [isOpen, setIsOpen] = useState(false);

  if (history.length === 0) {
    return null;
  }

  return (
    <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-2xl border border-gray-700 mt-10">
      <button 
        className="w-full flex justify-between items-center p-4 text-left"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
      >
        <div className="flex items-center gap-3">
          <HistoryIcon className="h-6 w-6 text-purple-400" />
          <h2 className="text-xl font-bold text-gray-200">
            سجل الإنشاء ({history.length})
          </h2>
        </div>
        <div className="flex items-center gap-4">
          {isOpen && history.length > 0 && (
             <button
                onClick={(e) => {
                    e.stopPropagation();
                    if (window.confirm('هل أنت متأكد من رغبتك في حذف كل السجل؟ لا يمكن التراجع عن هذا الإجراء.')) {
                        onClear();
                    }
                }}
                className="flex items-center gap-2 text-sm text-red-400 hover:text-red-300 transition-colors"
                aria-label="مسح كل السجل"
             >
                <TrashIcon className="h-4 w-4" />
                <span>مسح الكل</span>
            </button>
          )}
          <ChevronDownIcon className={`h-6 w-6 text-gray-400 transition-transform duration-300 ${isOpen ? 'transform rotate-180' : ''}`} />
        </div>
      </button>

      {isOpen && (
        <div className="p-4 border-t border-gray-700">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {history.map(item => (
              <HistoryItemCard key={item.id} item={item} onView={onView} onDelete={onDelete} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default History;
