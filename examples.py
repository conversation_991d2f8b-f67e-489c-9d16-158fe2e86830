"""
أمثلة على الأوصاف والإعدادات لتطبيق مولد الصور
"""

# أمثلة على الأوصاف الجيدة
GOOD_PROMPTS = {
    "طبيعة": [
        "A serene mountain landscape at sunset with golden light reflecting on a crystal clear lake",
        "Dense tropical rainforest with sunbeams filtering through the canopy, exotic birds flying",
        "Peaceful meadow filled with wildflowers, butterflies dancing in warm sunlight",
        "Majestic waterfall cascading down moss-covered rocks in an ancient forest",
        "Snow-capped mountains under a starry night sky with aurora borealis"
    ],
    
    "مدن وعمارة": [
        "Futuristic cyberpunk city with neon lights, flying cars, and towering skyscrapers",
        "Ancient medieval castle on a hilltop surrounded by morning mist",
        "Cozy European village with cobblestone streets and flower-filled balconies",
        "Modern glass building reflecting clouds and blue sky in urban setting",
        "Traditional Japanese temple surrounded by cherry blossoms in spring"
    ],
    
    "حيوانات": [
        "Majestic lion with flowing mane standing proudly on African savanna",
        "Cute red panda sleeping peacefully on a tree branch",
        "Colorful tropical fish swimming in coral reef underwater scene",
        "Wise old owl perched on ancient oak tree branch at twilight",
        "Playful dolphins jumping out of crystal blue ocean waves"
    ],
    
    "فن وخيال": [
        "Magical fairy garden with glowing mushrooms and sparkling fireflies",
        "Steampunk airship floating above Victorian city in golden hour",
        "Dragon soaring through cloudy sky with wings spread wide",
        "Enchanted forest portal glowing with mystical blue light",
        "Space station orbiting beautiful alien planet with multiple moons"
    ],
    
    "أشخاص وبورتريه": [
        "Portrait of wise elderly man with kind eyes and gentle smile",
        "Young woman with flowing hair dancing in field of sunflowers",
        "Child reading book under large tree in peaceful garden",
        "Artist painting landscape on easel in outdoor studio",
        "Musician playing violin on stage with dramatic lighting"
    ]
}

# أمثلة على النصوص السلبية المفيدة
NEGATIVE_PROMPTS = {
    "جودة عامة": "blurry, low quality, pixelated, distorted, ugly, bad anatomy, deformed",
    "نص ومائية": "text, watermark, signature, logo, username, copyright",
    "ألوان": "oversaturated, undersaturated, monochrome, black and white",
    "تشويه": "extra limbs, missing limbs, floating limbs, disconnected limbs, malformed hands",
    "أسلوب": "cartoon, anime, drawing, painting, sketch, 3d render"
}

# إعدادات مُحسنة لأنواع مختلفة من الصور
OPTIMIZED_SETTINGS = {
    "جودة عالية": {
        "steps": 50,
        "guidance": 8.0,
        "size": (768, 768),
        "description": "للحصول على أفضل جودة (بطيء)"
    },
    
    "متوازن": {
        "steps": 25,
        "guidance": 7.5,
        "size": (512, 512),
        "description": "توازن بين الجودة والسرعة"
    },
    
    "سريع": {
        "steps": 15,
        "guidance": 7.0,
        "size": (512, 512),
        "description": "توليد سريع (جودة أقل)"
    },
    
    "بورتريه": {
        "steps": 30,
        "guidance": 8.5,
        "size": (512, 768),
        "description": "مُحسن للوجوه والأشخاص"
    },
    
    "منظر طبيعي": {
        "steps": 25,
        "guidance": 7.0,
        "size": (768, 512),
        "description": "مُحسن للمناظر الطبيعية"
    }
}

# نصائح لكتابة أوصاف أفضل
WRITING_TIPS = [
    "استخدم صفات وصفية مفصلة (مثل: majestic, serene, vibrant)",
    "حدد الإضاءة (مثل: golden hour, soft lighting, dramatic shadows)",
    "اذكر الألوان المرغوبة (مثل: warm colors, cool blues, earth tones)",
    "حدد الأسلوب الفني (مثل: photorealistic, oil painting, digital art)",
    "اذكر التفاصيل البيئية (مثل: misty morning, starry night, sunny day)",
    "استخدم أسماء فنانين مشهورين للحصول على أسلوب معين",
    "حدد زاوية التصوير (مثل: close-up, wide shot, bird's eye view)",
    "اذكر المشاعر المرغوبة (مثل: peaceful, energetic, mysterious)"
]

# كلمات مفتاحية مفيدة
USEFUL_KEYWORDS = {
    "جودة": ["4k", "8k", "high resolution", "detailed", "sharp", "crisp"],
    "إضاءة": ["golden hour", "soft lighting", "dramatic lighting", "natural light", "studio lighting"],
    "ألوان": ["vibrant", "muted", "pastel", "monochromatic", "colorful", "warm tones", "cool tones"],
    "أسلوب": ["photorealistic", "hyperrealistic", "artistic", "cinematic", "professional"],
    "تركيب": ["rule of thirds", "symmetrical", "centered", "dynamic composition"],
    "كاميرا": ["DSLR", "professional photography", "macro lens", "wide angle", "telephoto"]
}

def get_random_prompt_by_category(category):
    """الحصول على وصف عشوائي من فئة معينة"""
    import random
    if category in GOOD_PROMPTS:
        return random.choice(GOOD_PROMPTS[category])
    return None

def get_optimized_settings(setting_type):
    """الحصول على إعدادات محسنة"""
    return OPTIMIZED_SETTINGS.get(setting_type, OPTIMIZED_SETTINGS["متوازن"])

def enhance_prompt(basic_prompt, style="photorealistic"):
    """تحسين الوصف بإضافة كلمات مفتاحية"""
    enhancements = {
        "photorealistic": "photorealistic, high quality, detailed, professional photography",
        "artistic": "artistic, beautiful, aesthetic, masterpiece",
        "cinematic": "cinematic lighting, dramatic, movie scene, professional",
        "fantasy": "fantasy art, magical, enchanted, mystical, ethereal"
    }
    
    enhancement = enhancements.get(style, enhancements["photorealistic"])
    return f"{basic_prompt}, {enhancement}"

if __name__ == "__main__":
    print("🎨 أمثلة على الأوصاف:")
    print("=" * 40)
    
    for category, prompts in GOOD_PROMPTS.items():
        print(f"\n📂 {category}:")
        for i, prompt in enumerate(prompts[:2], 1):  # عرض أول مثالين فقط
            print(f"   {i}. {prompt}")
    
    print(f"\n🔧 الإعدادات المحسنة:")
    print("=" * 40)
    
    for setting, config in OPTIMIZED_SETTINGS.items():
        print(f"\n⚙️  {setting}: {config['description']}")
        print(f"   خطوات: {config['steps']}, توجيه: {config['guidance']}, حجم: {config['size']}")
