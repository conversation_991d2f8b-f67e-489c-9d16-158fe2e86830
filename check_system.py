#!/usr/bin/env python3
"""
فحص النظام والمتطلبات لتطبيق مولد الصور
"""

import sys
import subprocess
import importlib
import platform
import psutil
import torch

def check_python_version():
    """فحص إصدار Python"""
    print("🐍 فحص إصدار Python...")
    version = sys.version_info
    print(f"   الإصدار: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 8:
        print("   ✅ إصدار Python مناسب")
        return True
    else:
        print("   ❌ يتطلب Python 3.8 أو أحدث")
        return False

def check_memory():
    """فحص الذاكرة المتاحة"""
    print("\n💾 فحص الذاكرة...")
    memory = psutil.virtual_memory()
    total_gb = memory.total / (1024**3)
    available_gb = memory.available / (1024**3)
    
    print(f"   إجمالي الذاكرة: {total_gb:.1f} جيجابايت")
    print(f"   الذاكرة المتاحة: {available_gb:.1f} جيجابايت")
    
    if total_gb >= 8:
        print("   ✅ الذاكرة كافية")
        return True
    else:
        print("   ⚠️  قد تحتاج ذاكرة أكثر للحصول على أفضل أداء")
        return False

def check_gpu():
    """فحص كرت الرسوميات"""
    print("\n🎮 فحص كرت الرسوميات...")
    
    try:
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            
            print(f"   عدد كروت الرسوميات: {gpu_count}")
            print(f"   اسم الكرت: {gpu_name}")
            print(f"   ذاكرة الكرت: {gpu_memory:.1f} جيجابايت")
            print("   ✅ كرت رسوميات متاح (سيتم استخدام GPU)")
            return True
        else:
            print("   ⚠️  لا يوجد كرت رسوميات متاح (سيتم استخدام CPU)")
            return False
    except Exception as e:
        print(f"   ❌ خطأ في فحص كرت الرسوميات: {e}")
        return False

def check_package(package_name):
    """فحص تثبيت مكتبة معينة"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def check_packages():
    """فحص المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_packages = [
        "torch",
        "torchvision", 
        "diffusers",
        "transformers",
        "accelerate",
        "PIL",
        "gradio",
        "numpy",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        if check_package(package):
            print(f"   ✅ {package}")
        else:
            print(f"   ❌ {package} (غير مثبت)")
            missing_packages.append(package)
    
    return missing_packages

def check_disk_space():
    """فحص المساحة المتاحة"""
    print("\n💿 فحص المساحة المتاحة...")
    
    disk_usage = psutil.disk_usage('.')
    free_gb = disk_usage.free / (1024**3)
    
    print(f"   المساحة المتاحة: {free_gb:.1f} جيجابايت")
    
    if free_gb >= 10:
        print("   ✅ المساحة كافية")
        return True
    else:
        print("   ⚠️  قد تحتاج مساحة أكثر لتحميل النماذج")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🔍 فحص النظام لتطبيق مولد الصور بالذكاء الاصطناعي")
    print("=" * 50)
    
    # فحص النظام
    python_ok = check_python_version()
    memory_ok = check_memory()
    gpu_available = check_gpu()
    missing_packages = check_packages()
    disk_ok = check_disk_space()
    
    print("\n" + "=" * 50)
    print("📋 ملخص النتائج:")
    print("=" * 50)
    
    if python_ok:
        print("✅ Python: مناسب")
    else:
        print("❌ Python: يحتاج تحديث")
    
    if memory_ok:
        print("✅ الذاكرة: كافية")
    else:
        print("⚠️  الذاكرة: قد تحتاج أكثر")
    
    if gpu_available:
        print("✅ كرت الرسوميات: متاح")
    else:
        print("⚠️  كرت الرسوميات: غير متاح (سيعمل على CPU)")
    
    if not missing_packages:
        print("✅ المكتبات: جميعها مثبتة")
    else:
        print(f"❌ المكتبات: ناقص {len(missing_packages)} مكتبة")
        print("   لتثبيت المكتبات الناقصة، شغل: pip install -r requirements.txt")
    
    if disk_ok:
        print("✅ المساحة: كافية")
    else:
        print("⚠️  المساحة: قد تحتاج أكثر")
    
    print("\n" + "=" * 50)
    
    if python_ok and not missing_packages:
        print("🎉 النظام جاهز! يمكنك تشغيل التطبيق الآن")
        print("   شغل الأمر: python text_to_image_app.py")
    else:
        print("⚠️  يرجى حل المشاكل المذكورة أعلاه قبل تشغيل التطبيق")
    
    print("=" * 50)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 تم إلغاء الفحص")
    except Exception as e:
        print(f"\n❌ خطأ في الفحص: {e}")
    
    input("\nاضغط Enter للخروج...")
