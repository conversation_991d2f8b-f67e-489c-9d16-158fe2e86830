@echo off
chcp 65001 >nul
echo ========================================
echo    مولد الصور بالذكاء الاصطناعي
echo    Text to Image AI Generator
echo ========================================
echo.

echo هل تريد فحص النظام أولاً؟ (y/n)
set /p check_system="الإجابة: "

if /i "%check_system%"=="y" (
    echo.
    echo جاري فحص النظام...
    python check_system.py
    echo.
    echo اضغط أي مفتاح للمتابعة...
    pause >nul
)

echo.
echo جاري تشغيل التطبيق...
echo سيتم فتح المتصفح تلقائياً على العنوان: http://localhost:7860
echo.
echo ملاحظات مهمة:
echo - تأكد من تحميل النموذج أولاً من داخل التطبيق
echo - قد يستغرق التحميل الأول عدة دقائق
echo - استخدم Ctrl+C لإيقاف التطبيق
echo.

python text_to_image_app.py

echo.
echo تم إغلاق التطبيق
pause
