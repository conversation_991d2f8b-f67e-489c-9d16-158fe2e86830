#!/usr/bin/env python3
"""
مولد الصور بالذكاء الاصطناعي - نسخة API فقط
"""

import gradio as gr
import requests
import json
import base64
import io
import os
from PIL import Image
from datetime import datetime
import time

# إنشاء مجلد للصور
if not os.path.exists("generated_images"):
    os.makedirs("generated_images")

class APIImageGenerator:
    def __init__(self):
        pass
    
    def generate_with_openai(self, api_key, prompt, model="dall-e-3", size="1024x1024", quality="standard"):
        """توليد صورة باستخدام OpenAI DALL-E"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "prompt": prompt,
                "n": 1,
                "size": size,
                "quality": quality
            }
            
            print(f"🎨 جاري إرسال الطلب إلى OpenAI...")
            response = requests.post(
                "https://api.openai.com/v1/images/generations",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                image_url = result["data"][0]["url"]
                
                print(f"📥 جاري تحميل الصورة...")
                # تحميل الصورة
                img_response = requests.get(image_url)
                image = Image.open(io.BytesIO(img_response.content))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"openai_{model}_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام {model}!\n📁 حُفظت في: {filepath}"
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get("error", {}).get("message", "خطأ غير معروف")
                except:
                    error_msg = f"HTTP {response.status_code}"
                return None, f"❌ خطأ من OpenAI: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ OpenAI: {str(e)}"
    
    def generate_with_stability(self, api_key, prompt, negative_prompt="", model="stable-diffusion-xl-1024-v1-0"):
        """توليد صورة باستخدام Stability AI"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "text_prompts": [
                    {"text": prompt, "weight": 1.0}
                ],
                "cfg_scale": 7,
                "height": 1024,
                "width": 1024,
                "samples": 1,
                "steps": 30
            }
            
            if negative_prompt:
                data["text_prompts"].append({"text": negative_prompt, "weight": -1.0})
            
            print(f"🎨 جاري إرسال الطلب إلى Stability AI...")
            response = requests.post(
                f"https://api.stability.ai/v1/generation/{model}/text-to-image",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                image_data = base64.b64decode(result["artifacts"][0]["base64"])
                image = Image.open(io.BytesIO(image_data))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"stability_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام Stability AI!\n📁 حُفظت في: {filepath}"
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get("message", "خطأ غير معروف")
                except:
                    error_msg = f"HTTP {response.status_code}"
                return None, f"❌ خطأ من Stability AI: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ Stability AI: {str(e)}"
    
    def generate_with_huggingface(self, api_key, prompt, model="runwayml/stable-diffusion-v1-5"):
        """توليد صورة باستخدام Hugging Face"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {"inputs": prompt}
            
            print(f"🎨 جاري إرسال الطلب إلى Hugging Face...")
            response = requests.post(
                f"https://api-inference.huggingface.co/models/{model}",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                image = Image.open(io.BytesIO(response.content))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"huggingface_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام Hugging Face!\n📁 حُفظت في: {filepath}"
            else:
                try:
                    error_data = response.json()
                    error_msg = error_data.get("error", "خطأ غير معروف")
                except:
                    error_msg = f"HTTP {response.status_code}"
                return None, f"❌ خطأ من Hugging Face: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ Hugging Face: {str(e)}"

# إنشاء مثيل من المولد
generator = APIImageGenerator()

def generate_image(service_type, api_key, prompt, negative_prompt, model_choice, 
                  openai_size, openai_quality):
    """واجهة توليد الصورة"""
    if not prompt.strip():
        return None, "❌ يرجى إدخال وصف للصورة"
    
    if not api_key.strip():
        return None, f"❌ يرجى إدخال مفتاح {service_type} API"
    
    if service_type == "OpenAI DALL-E":
        return generator.generate_with_openai(api_key, prompt, model_choice, openai_size, openai_quality)
    
    elif service_type == "Stability AI":
        return generator.generate_with_stability(api_key, prompt, negative_prompt, model_choice)
    
    elif service_type == "Hugging Face":
        return generator.generate_with_huggingface(api_key, prompt, model_choice)
    
    else:
        return None, "❌ نوع الخدمة غير مدعوم"

def update_model_choices(service_type):
    """تحديث خيارات النماذج حسب نوع الخدمة"""
    models = {
        "OpenAI DALL-E": ["dall-e-3", "dall-e-2"],
        "Stability AI": ["stable-diffusion-xl-1024-v1-0", "stable-diffusion-v1-6"],
        "Hugging Face": ["runwayml/stable-diffusion-v1-5", "stabilityai/stable-diffusion-2-1", "prompthero/openjourney"]
    }
    return gr.Dropdown(choices=models.get(service_type, []), value=models.get(service_type, [""])[0])

def update_ui_visibility(service_type):
    """تحديث ظهور عناصر الواجهة حسب نوع الخدمة"""
    is_openai = service_type == "OpenAI DALL-E"
    return gr.update(visible=is_openai)

# إنشاء واجهة Gradio
with gr.Blocks(title="مولد الصور بالذكاء الاصطناعي - API", theme=gr.themes.Soft()) as app:
    gr.Markdown("""
    # 🎨 مولد الصور بالذكاء الاصطناعي
    ## باستخدام خدمات API المتقدمة
    
    🚀 **سريع وعالي الجودة** | 🔑 **يتطلب مفتاح API** | 💰 **مدفوع حسب الاستخدام**
    """)
    
    with gr.Row():
        with gr.Column():
            # اختيار نوع الخدمة
            service_type = gr.Dropdown(
                choices=["OpenAI DALL-E", "Stability AI", "Hugging Face"],
                value="OpenAI DALL-E",
                label="🔧 نوع الخدمة"
            )
            
            # مفتاح API
            api_key_input = gr.Textbox(
                label="🔑 مفتاح API",
                placeholder="أدخل مفتاح API هنا...",
                type="password"
            )
            
            # اختيار النموذج
            model_choice = gr.Dropdown(
                choices=["dall-e-3", "dall-e-2"],
                value="dall-e-3",
                label="🤖 النموذج"
            )
            
            gr.Markdown("### 📝 إعدادات الصورة")
            
            # إدخال النص
            prompt_input = gr.Textbox(
                label="وصف الصورة (بالإنجليزية)",
                placeholder="مثال: A beautiful sunset over mountains with orange clouds",
                lines=3
            )
            
            negative_prompt_input = gr.Textbox(
                label="ما لا تريده في الصورة (اختياري)",
                placeholder="مثال: blurry, low quality, distorted",
                lines=2
            )
            
            # إعدادات OpenAI
            with gr.Group(visible=True) as openai_settings:
                gr.Markdown("#### ⚙️ إعدادات OpenAI")
                openai_size = gr.Dropdown(
                    choices=["1024x1024", "1024x1792", "1792x1024"],
                    value="1024x1024",
                    label="📐 حجم الصورة"
                )
                openai_quality = gr.Dropdown(
                    choices=["standard", "hd"],
                    value="hd",
                    label="✨ جودة الصورة"
                )
            
            generate_btn = gr.Button("🎨 توليد الصورة", variant="primary", size="lg")
        
        with gr.Column():
            # عرض النتائج
            output_image = gr.Image(label="🖼️ الصورة المولدة", height=500)
            output_message = gr.Textbox(label="📋 الرسائل", interactive=False, lines=4)
    
    # معلومات الخدمات
    with gr.Accordion("📋 معلومات الخدمات ومفاتيح API", open=False):
        gr.Markdown("""
        ### 🔑 كيفية الحصول على مفاتيح API:
        
        #### 🎯 **OpenAI DALL-E** (الأفضل للجودة)
        - 🌐 **الموقع**: https://platform.openai.com/api-keys
        - 💰 **التكلفة**: $0.040 لكل صورة (DALL-E 3) | $0.020 (DALL-E 2)
        - ✅ **المميزات**: جودة عالية جداً، فهم ممتاز للنصوص، سرعة عالية
        
        #### ⚡ **Stability AI** (الأفضل للتوازن)
        - 🌐 **الموقع**: https://platform.stability.ai/account/keys
        - 💰 **التكلفة**: ~$0.05 لكل صورة + رصيد مجاني $25
        - ✅ **المميزات**: جودة عالية، سرعة جيدة، تحكم متقدم
        
        #### 🆓 **Hugging Face** (الأفضل للتجريب)
        - 🌐 **الموقع**: https://huggingface.co/settings/tokens
        - 💰 **التكلفة**: مجاني مع حدود استخدام
        - ✅ **المميزات**: مجاني، نماذج متنوعة
        
        ### 💡 **نصائح للاستخدام:**
        - 🎯 **للمبتدئين**: ابدأ بـ Hugging Face
        - 💼 **للاستخدام المهني**: استخدم OpenAI DALL-E 3
        - ⚖️ **للتوازن**: استخدم Stability AI
        - 🔒 **الأمان**: لا تشارك مفاتيح API مع أحد
        """)
    
    # أمثلة سريعة
    with gr.Accordion("💡 أمثلة سريعة", open=False):
        examples = [
            "A majestic lion with golden mane standing on African savanna at sunset",
            "A cozy cabin in snowy mountains with warm lights glowing from windows",
            "A futuristic cyberpunk city with neon lights and flying cars",
            "A magical forest with glowing mushrooms and fairy lights at night",
            "A peaceful Japanese garden with cherry blossoms and a small bridge"
        ]
        
        for i, example in enumerate(examples, 1):
            gr.Markdown(f"**{i}.** `{example}`")
    
    # ربط الأحداث
    service_type.change(
        fn=update_ui_visibility,
        inputs=service_type,
        outputs=openai_settings
    )
    
    service_type.change(
        fn=update_model_choices,
        inputs=service_type,
        outputs=model_choice
    )
    
    generate_btn.click(
        fn=generate_image,
        inputs=[
            service_type, api_key_input, prompt_input, negative_prompt_input,
            model_choice, openai_size, openai_quality
        ],
        outputs=[output_image, output_message]
    )

if __name__ == "__main__":
    print("🎨 مولد الصور بالذكاء الاصطناعي - نسخة API")
    print("🚀 جاري تشغيل التطبيق...")
    print("📱 سيتم فتح المتصفح على: http://localhost:7862")
    print("🔑 تأكد من وجود مفتاح API صالح")
    
    app.launch(
        server_name="127.0.0.1",
        server_port=7862,
        share=False,
        inbrowser=True
    )
