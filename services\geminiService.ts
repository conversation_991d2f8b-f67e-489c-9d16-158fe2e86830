import { AspectRatio, GeneratedResult } from "../types";

const getDimensions = (aspectRatio: AspectRatio): { width: number; height: number } => {
    // Standard SDXL resolutions
    switch (aspectRatio) {
        case '16:9': return { width: 1344, height: 768 };
        case '9:16': return { width: 768, height: 1344 };
        case '4:3': return { width: 1152, height: 896 };
        case '3:4': return { width: 896, height: 1152 };
        case '1:1':
        default:
            return { width: 1024, height: 1024 };
    }
};

async function translateToEnglish(text: string): Promise<string> {
    if (!text.trim()) return '';

    // Check if the text is already mostly English to avoid unnecessary API calls
    const englishChars = (text.match(/[a-zA-Z]/g) || []).length;
    if (englishChars / text.length > 0.8) {
        return text;
    }

    try {
        const response = await fetch("https://api.together.xyz/v1/chat/completions", {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bear<PERSON> ${process.env.TOGETHER_API_KEY}`
            },
            body: JSON.stringify({
                model: 'meta-llama/Llama-3-8b-chat-hf',
                messages: [
                    { role: 'system', content: "You are an expert translator. Translate the given Arabic text to English. Return ONLY the translated English text, without any additional explanations, introductory phrases, or quotes." },
                    { role: 'user', content: text }
                ],
                temperature: 0.1,
                max_tokens: 300,
            }),
        });

        if (!response.ok) {
            const errorData = await response.json();
            console.error("Translation API error:", errorData);
            throw new Error(errorData.error?.message || 'Translation request failed');
        }

        const result = await response.json();
        return result.choices[0].message.content.trim();
    } catch (error) {
        console.error("Translation with Together AI failed:", error);
        return text; // Fallback to original text
    }
}

export async function generateImages(
  prompt: string,
  negativePrompt: string,
  aspectRatio: AspectRatio,
  numberOfImages: number
): Promise<GeneratedResult> {

  const needsTranslation = /[\u0600-\u06FF]/.test(prompt) || /[\u0600-\u06FF]/.test(negativePrompt);
  
  let effectivePrompt = prompt;
  let effectiveNegativePrompt = negativePrompt;

  if (needsTranslation) {
      [effectivePrompt, effectiveNegativePrompt] = await Promise.all([
          translateToEnglish(prompt),
          translateToEnglish(negativePrompt)
      ]);
  }

  const { width, height } = getDimensions(aspectRatio);

  try {
    const response = await fetch("https://api.together.xyz/v1/images/generations", {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.TOGETHER_API_KEY}`
        },
        body: JSON.stringify({
            model: 'stabilityai/stable-diffusion-xl-base-1.0',
            prompt: effectivePrompt,
            negative_prompt: effectiveNegativePrompt.trim() || undefined,
            n: numberOfImages,
            width: width,
            height: height,
        }),
    });

    if (!response.ok) {
        const errorData = await response.json();
        console.error("Image Generation API error:", errorData);
        throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
    }

    const result = await response.json();

    if (!result.data || result.data.length === 0) {
      throw new Error("لم يتم إنشاء أي صور. قد يكون المحتوى غير مسموح به أو أن الوصف غير واضح.");
    }

    const images = result.data.map((img: { b64_json: string }) => {
      return `data:image/jpeg;base64,${img.b64_json}`;
    });

    return { images, usedPrompt: effectivePrompt, originalPrompt: prompt };

  } catch (error) {
    console.error("Error generating images with Together AI:", error);
    if (error instanceof Error) {
       throw new Error(`فشل إنشاء الصورة: ${error.message}`);
    }
    throw new Error("حدث خطأ غير متوقع أثناء الاتصال بـ Together AI.");
  }
}