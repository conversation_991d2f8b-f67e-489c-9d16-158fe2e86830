# مولد الصور بالذكاء الاصطناعي 🎨

تطبيق مجاني لتحويل النص إلى صورة باستخدام تقنية Stable Diffusion والذكاء الاصطناعي.

## المميزات

- 🆓 **مجاني تماماً** - يستخدم نماذج مفتوحة المصدر
- 🎯 **سهل الاستخدام** - واجهة بسيطة باللغة العربية
- ⚡ **سريع** - تحسينات للأداء والذاكرة
- 🎨 **قابل للتخصيص** - إعدادات متقدمة للتحكم في الجودة
- 💾 **يعمل محلياً** - لا يحتاج اتصال بالإنترنت بعد التحميل

## متطلبات النظام

- Python 3.8 أو أحدث
- 8 جيجابايت RAM على الأقل
- كرت رسوميات NVIDIA (اختياري لتسريع العملية)
- 10 جيجابايت مساحة فارغة

## التثبيت

### 1. تثبيت Python
تأكد من تثبيت Python 3.8+ على نظامك.

### 2. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 3. تشغيل التطبيق
```bash
python text_to_image_app.py
```

## كيفية الاستخدام

1. **تشغيل التطبيق**: شغل الملف `text_to_image_app.py`
2. **تحميل النموذج**: اضغط على زر "تحميل النموذج" (قد يستغرق 5-10 دقائق في المرة الأولى)
3. **كتابة الوصف**: اكتب وصفاً للصورة باللغة الإنجليزية
4. **ضبط الإعدادات**: اختر الحجم وعدد الخطوات حسب رغبتك
5. **توليد الصورة**: اضغط على "توليد الصورة"

## أمثلة على الأوصاف

- `A beautiful sunset over mountains with orange and pink clouds`
- `A cute cat sitting in a garden with colorful flowers`
- `A futuristic city with flying cars and neon lights`
- `A peaceful lake surrounded by autumn trees`

## الإعدادات المتقدمة

- **عدد خطوات التوليد**: أكثر = جودة أفضل (لكن أبطأ)
- **قوة التوجيه**: أعلى = التزام أكبر بالوصف
- **الحجم**: أكبر = تفاصيل أكثر (لكن يحتاج ذاكرة أكثر)
- **البذرة**: نفس الرقم = نفس النتيجة

## استكشاف الأخطاء

### خطأ في الذاكرة
- قلل حجم الصورة إلى 256x256
- أغلق البرامج الأخرى
- استخدم CPU بدلاً من GPU

### بطء في التوليد
- قلل عدد الخطوات إلى 10-15
- استخدم حجم أصغر للصورة
- تأكد من تثبيت CUDA للـ GPU

### جودة ضعيفة
- زد عدد الخطوات إلى 30-50
- استخدم وصفاً أكثر تفصيلاً
- جرب قيم مختلفة لقوة التوجيه

## الملفات المهمة

- `text_to_image_app.py`: الملف الرئيسي للتطبيق
- `requirements.txt`: قائمة المكتبات المطلوبة
- `README.md`: دليل الاستخدام

## الدعم الفني

إذا واجهت أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تحقق من توفر مساحة كافية
3. جرب إعادة تشغيل التطبيق

## الترخيص

هذا المشروع مجاني ومفتوح المصدر. يمكنك استخدامه وتعديله بحرية.

---

**ملاحظة**: النموذج المستخدم (Stable Diffusion) مدرب على نصوص إنجليزية، لذا يُنصح بكتابة الأوصاف بالإنجليزية للحصول على أفضل النتائج.
