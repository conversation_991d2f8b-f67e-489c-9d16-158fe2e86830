import React from 'react';
import { HistoryItem as HistoryItemType } from '../types';
import TrashIcon from './icons/TrashIcon';

interface HistoryItemProps {
  item: HistoryItemType;
  onView: (item: HistoryItemType) => void;
  onDelete: (id: string) => void;
}

const HistoryItemCard: React.FC<HistoryItemProps> = ({ item, onView, onDelete }) => {
  const formattedDate = new Date(item.timestamp).toLocaleDateString('ar-EG', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  const stopPropagationAndDelete = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    onDelete(item.id);
  };

  return (
    <div 
      className="bg-gray-800 rounded-lg overflow-hidden cursor-pointer group border border-gray-700 hover:border-purple-500 transition-all duration-300"
      onClick={() => onView(item)}
      role="button"
      tabIndex={0}
      aria-label={`عرض نتيجة الإنشاء للوصف: ${item.originalPrompt}`}
    >
      <div className="relative">
        <img src={item.images[0]} alt={item.originalPrompt} className="w-full h-40 object-cover" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
        <button 
          onClick={stopPropagationAndDelete}
          className="absolute top-2 right-2 bg-red-600/70 hover:bg-red-500 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
          aria-label="حذف هذا العنصر"
          title="حذف"
        >
          <TrashIcon className="h-4 w-4" />
        </button>
      </div>
      <div className="p-4">
        <p className="text-gray-300 text-sm truncate" title={item.originalPrompt}>
          {item.originalPrompt}
        </p>
        <p className="text-gray-500 text-xs mt-1">{formattedDate}</p>
      </div>
    </div>
  );
};

export default HistoryItemCard;
