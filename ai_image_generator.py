#!/usr/bin/env python3
"""
مولد الصور بالذكاء الاصطناعي مع دعم API
"""

import gradio as gr
import torch
import requests
import json
import base64
import io
import os
from PIL import Image
from datetime import datetime
import time

# إنشاء مجلد للصور
if not os.path.exists("generated_images"):
    os.makedirs("generated_images")

class AIImageGenerator:
    def __init__(self):
        self.local_pipe = None
        self.local_model_loaded = False
        
    def load_local_model(self):
        """تحميل النموذج المحلي"""
        try:
            from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
            
            print("📥 جاري تحميل النموذج المحلي...")
            model_id = "runwayml/stable-diffusion-v1-5"
            
            self.local_pipe = StableDiffusionPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )
            
            self.local_pipe.scheduler = DPMSolverMultistepScheduler.from_config(
                self.local_pipe.scheduler.config
            )
            
            device = "cuda" if torch.cuda.is_available() else "cpu"
            self.local_pipe = self.local_pipe.to(device)
            
            self.local_model_loaded = True
            return "✅ تم تحميل النموذج المحلي بنجاح!"
            
        except Exception as e:
            return f"❌ خطأ في تحميل النموذج المحلي: {str(e)}"
    
    def generate_with_openai(self, api_key, prompt, model="dall-e-3", size="1024x1024", quality="standard"):
        """توليد صورة باستخدام OpenAI DALL-E"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "prompt": prompt,
                "n": 1,
                "size": size,
                "quality": quality
            }
            
            response = requests.post(
                "https://api.openai.com/v1/images/generations",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                image_url = result["data"][0]["url"]
                
                # تحميل الصورة
                img_response = requests.get(image_url)
                image = Image.open(io.BytesIO(img_response.content))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"openai_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام {model}! حُفظت في: {filepath}"
            else:
                error_msg = response.json().get("error", {}).get("message", "خطأ غير معروف")
                return None, f"❌ خطأ من OpenAI: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ OpenAI: {str(e)}"
    
    def generate_with_stability(self, api_key, prompt, negative_prompt="", model="stable-diffusion-xl-1024-v1-0"):
        """توليد صورة باستخدام Stability AI"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "text_prompts": [
                    {"text": prompt, "weight": 1.0}
                ],
                "cfg_scale": 7,
                "height": 1024,
                "width": 1024,
                "samples": 1,
                "steps": 30
            }
            
            if negative_prompt:
                data["text_prompts"].append({"text": negative_prompt, "weight": -1.0})
            
            response = requests.post(
                f"https://api.stability.ai/v1/generation/{model}/text-to-image",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                image_data = base64.b64decode(result["artifacts"][0]["base64"])
                image = Image.open(io.BytesIO(image_data))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"stability_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام Stability AI! حُفظت في: {filepath}"
            else:
                error_msg = response.json().get("message", "خطأ غير معروف")
                return None, f"❌ خطأ من Stability AI: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ Stability AI: {str(e)}"
    
    def generate_with_huggingface(self, api_key, prompt, model="runwayml/stable-diffusion-v1-5"):
        """توليد صورة باستخدام Hugging Face"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            data = {"inputs": prompt}
            
            response = requests.post(
                f"https://api-inference.huggingface.co/models/{model}",
                headers=headers,
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                image = Image.open(io.BytesIO(response.content))
                
                # حفظ الصورة
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"huggingface_{timestamp}.png"
                filepath = os.path.join("generated_images", filename)
                image.save(filepath)
                
                return image, f"✅ تم توليد الصورة بنجاح باستخدام Hugging Face! حُفظت في: {filepath}"
            else:
                try:
                    error_msg = response.json().get("error", "خطأ غير معروف")
                except:
                    error_msg = f"HTTP {response.status_code}"
                return None, f"❌ خطأ من Hugging Face: {error_msg}"
                
        except Exception as e:
            return None, f"❌ خطأ في الاتصال بـ Hugging Face: {str(e)}"
    
    def generate_local(self, prompt, negative_prompt="", steps=20, guidance=7.5, width=512, height=512):
        """توليد صورة باستخدام النموذج المحلي"""
        if not self.local_model_loaded:
            return None, "❌ يجب تحميل النموذج المحلي أولاً"
        
        try:
            with torch.autocast("cuda" if torch.cuda.is_available() else "cpu"):
                image = self.local_pipe(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    num_inference_steps=int(steps),
                    guidance_scale=float(guidance),
                    width=int(width),
                    height=int(height)
                ).images[0]
            
            # حفظ الصورة
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"local_{timestamp}.png"
            filepath = os.path.join("generated_images", filename)
            image.save(filepath)
            
            return image, f"✅ تم توليد الصورة بنجاح محلياً! حُفظت في: {filepath}"
            
        except Exception as e:
            return None, f"❌ خطأ في التوليد المحلي: {str(e)}"

# إنشاء مثيل من المولد
generator = AIImageGenerator()

def load_local_model():
    """واجهة تحميل النموذج المحلي"""
    return generator.load_local_model()

def generate_image(service_type, api_key, prompt, negative_prompt, model_choice, 
                  openai_size, openai_quality, steps, guidance, width, height):
    """واجهة توليد الصورة"""
    if not prompt.strip():
        return None, "❌ يرجى إدخال وصف للصورة"
    
    if service_type == "محلي":
        return generator.generate_local(prompt, negative_prompt, steps, guidance, width, height)
    
    elif service_type == "OpenAI DALL-E":
        if not api_key.strip():
            return None, "❌ يرجى إدخال مفتاح OpenAI API"
        return generator.generate_with_openai(api_key, prompt, model_choice, openai_size, openai_quality)
    
    elif service_type == "Stability AI":
        if not api_key.strip():
            return None, "❌ يرجى إدخال مفتاح Stability AI API"
        return generator.generate_with_stability(api_key, prompt, negative_prompt, model_choice)
    
    elif service_type == "Hugging Face":
        if not api_key.strip():
            return None, "❌ يرجى إدخال مفتاح Hugging Face API"
        return generator.generate_with_huggingface(api_key, prompt, model_choice)
    
    else:
        return None, "❌ نوع الخدمة غير مدعوم"

def update_model_choices(service_type):
    """تحديث خيارات النماذج حسب نوع الخدمة"""
    models = {
        "محلي": ["stable-diffusion-v1-5"],
        "OpenAI DALL-E": ["dall-e-3", "dall-e-2"],
        "Stability AI": ["stable-diffusion-xl-1024-v1-0", "stable-diffusion-v1-6"],
        "Hugging Face": ["runwayml/stable-diffusion-v1-5", "stabilityai/stable-diffusion-2-1"]
    }
    return gr.Dropdown(choices=models.get(service_type, []), value=models.get(service_type, [""])[0])

def update_ui_visibility(service_type):
    """تحديث ظهور عناصر الواجهة حسب نوع الخدمة"""
    is_local = service_type == "محلي"
    is_openai = service_type == "OpenAI DALL-E"

    return (
        gr.update(visible=not is_local),  # API key
        gr.update(visible=is_local),      # Local settings
        gr.update(visible=is_openai),     # OpenAI settings
        gr.update(visible=is_local)       # Load model button
    )

# إنشاء واجهة Gradio
with gr.Blocks(title="مولد الصور بالذكاء الاصطناعي مع API", theme=gr.themes.Soft()) as app:
    gr.Markdown("""
    # 🎨 مولد الصور بالذكاء الاصطناعي
    ## مع دعم خدمات API المختلفة
    """)

    with gr.Row():
        with gr.Column():
            # اختيار نوع الخدمة
            service_type = gr.Dropdown(
                choices=["محلي", "OpenAI DALL-E", "Stability AI", "Hugging Face"],
                value="محلي",
                label="نوع الخدمة"
            )

            # مفتاح API
            api_key_input = gr.Textbox(
                label="مفتاح API",
                placeholder="أدخل مفتاح API هنا...",
                type="password",
                visible=False
            )

            # زر تحميل النموذج المحلي
            load_model_btn = gr.Button("تحميل النموذج المحلي", variant="primary", visible=True)
            load_status = gr.Textbox(label="حالة التحميل", interactive=False)

            # اختيار النموذج
            model_choice = gr.Dropdown(
                choices=["stable-diffusion-v1-5"],
                value="stable-diffusion-v1-5",
                label="النموذج"
            )

            gr.Markdown("### إعدادات الصورة")

            # إدخال النص
            prompt_input = gr.Textbox(
                label="وصف الصورة (بالإنجليزية)",
                placeholder="مثال: A beautiful sunset over mountains",
                lines=3
            )

            negative_prompt_input = gr.Textbox(
                label="ما لا تريده في الصورة (اختياري)",
                placeholder="مثال: blurry, low quality",
                lines=2
            )

            # إعدادات OpenAI
            with gr.Group(visible=False) as openai_settings:
                gr.Markdown("#### إعدادات OpenAI")
                openai_size = gr.Dropdown(
                    choices=["1024x1024", "1024x1792", "1792x1024"],
                    value="1024x1024",
                    label="حجم الصورة"
                )
                openai_quality = gr.Dropdown(
                    choices=["standard", "hd"],
                    value="standard",
                    label="جودة الصورة"
                )

            # إعدادات محلية
            with gr.Group(visible=True) as local_settings:
                gr.Markdown("#### إعدادات التوليد المحلي")
                with gr.Row():
                    steps_slider = gr.Slider(
                        minimum=10, maximum=50, value=20, step=1,
                        label="عدد خطوات التوليد"
                    )
                    guidance_slider = gr.Slider(
                        minimum=1, maximum=20, value=7.5, step=0.5,
                        label="قوة التوجيه"
                    )

                with gr.Row():
                    width_slider = gr.Slider(
                        minimum=256, maximum=768, value=512, step=64,
                        label="العرض"
                    )
                    height_slider = gr.Slider(
                        minimum=256, maximum=768, value=512, step=64,
                        label="الارتفاع"
                    )

            generate_btn = gr.Button("توليد الصورة", variant="secondary", size="lg")

        with gr.Column():
            # عرض النتائج
            output_image = gr.Image(label="الصورة المولدة", height=400)
            output_message = gr.Textbox(label="الرسائل", interactive=False, lines=3)

    # معلومات الخدمات
    with gr.Accordion("📋 معلومات الخدمات", open=False):
        gr.Markdown("""
        ### 🔑 كيفية الحصول على مفاتيح API:

        **OpenAI DALL-E:**
        - اذهب إلى: https://platform.openai.com/api-keys
        - أنشئ حساب وأضف مفتاح API جديد
        - التكلفة: حوالي $0.04 لكل صورة (DALL-E 3)

        **Stability AI:**
        - اذهب إلى: https://platform.stability.ai/account/keys
        - أنشئ حساب واحصل على مفتاح API
        - التكلفة: حوالي $0.05 لكل صورة

        **Hugging Face:**
        - اذهب إلى: https://huggingface.co/settings/tokens
        - أنشئ حساب واحصل على Access Token
        - مجاني مع حدود استخدام

        ### 💡 نصائح:
        - **للاستخدام المجاني**: استخدم الخدمة المحلية
        - **للجودة العالية**: استخدم OpenAI DALL-E 3
        - **للسرعة**: استخدم Hugging Face أو Stability AI
        """)

    # ربط الأحداث
    service_type.change(
        fn=update_ui_visibility,
        inputs=service_type,
        outputs=[api_key_input, local_settings, openai_settings, load_model_btn]
    )

    service_type.change(
        fn=update_model_choices,
        inputs=service_type,
        outputs=model_choice
    )

    load_model_btn.click(
        fn=load_local_model,
        outputs=load_status
    )

    generate_btn.click(
        fn=generate_image,
        inputs=[
            service_type, api_key_input, prompt_input, negative_prompt_input,
            model_choice, openai_size, openai_quality, steps_slider,
            guidance_slider, width_slider, height_slider
        ],
        outputs=[output_image, output_message]
    )

if __name__ == "__main__":
    print("🎨 مولد الصور بالذكاء الاصطناعي مع دعم API")
    print("🚀 جاري تشغيل التطبيق...")
    print("📱 سيتم فتح المتصفح على: http://localhost:7861")

    app.launch(
        server_name="127.0.0.1",
        server_port=7861,
        share=False,
        inbrowser=True
    )
